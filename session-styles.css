/* Session Management Styles */

.sessions-container {
  margin-top: 20px;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.sessions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 5px;
}

.sessions-header h3 {
  font-size: 0.95rem;
  margin: 0;
  color: var(--text-primary);
  font-weight: 500;
}

.new-session-button {
  background: transparent;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 2px 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.new-session-button:hover {
  background-color: var(--accent-light);
}

.sessions-list {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) transparent;
}

.sessions-list::-webkit-scrollbar {
  width: 4px;
}

.sessions-list::-webkit-scrollbar-track {
  background: transparent;
}

.sessions-list::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: 4px;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.session-item:hover {
  background-color: var(--hover-color);
}

.session-item.active {
  background-color: var(--accent-light);
}

.session-info {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.session-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.session-date {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 2px;
}

.session-actions {
  display: flex;
  align-items: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.session-action-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 3px;
  border-radius: 4px;
  margin-left: 5px;
  transition: all 0.2s ease;
}

.session-action-button:hover {
  color: var(--accent-color);
  background-color: var(--accent-light);
}

.session-action-button.delete:hover {
  color: var(--error-color);
  background-color: rgba(255, 0, 0, 0.1);
}

/* Session creation modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  background: var(--bg-primary);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  padding: 20px;
  width: 350px;
  max-width: 90%;
  transform: translateY(-20px);
  transition: all 0.3s ease;
}

.modal-overlay.active .modal-container {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.modal-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
}

.modal-body {
  margin-bottom: 20px;
}

.modal-body input {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-button {
  padding: 8px 15px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.modal-button.cancel {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-button.confirm {
  background-color: var(--accent-color);
  color: white;
}

.modal-button:hover {
  opacity: 0.9;
}

/* Empty state */
.empty-sessions {
  padding: 15px 10px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.empty-sessions-hint {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 5px;
}

/* Keep button styles for backward compatibility */
.empty-sessions button {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: var(--accent-light);
  color: var(--accent-color);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.empty-sessions button:hover {
  background-color: var(--accent-color);
  color: white;
}
